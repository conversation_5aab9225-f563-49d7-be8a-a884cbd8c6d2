{"cells": [{"cell_type": "markdown", "id": "009d4b66", "metadata": {}, "source": ["BÀi 1"]}, {"cell_type": "code", "execution_count": null, "id": "21b1c5cc", "metadata": {}, "outputs": [], "source": ["import nltk\n", "nltk.download('punkt')\n", "nltk.download('stopwords')\n", "nltk.download('wordnet')\n", "nltk.download('averaged_perceptron_tagger')\n", "nltk.download('maxent_ne_chunker')\n", "nltk.download('words')"]}, {"cell_type": "code", "execution_count": null, "id": "dd28d751", "metadata": {}, "outputs": [], "source": ["input =\"Thuy Loi university is the best university in the world.\""]}, {"cell_type": "code", "execution_count": null, "id": "71620019", "metadata": {}, "outputs": [], "source": ["words = nltk.word_tokenize(input)\n", "print(words)"]}, {"cell_type": "code", "execution_count": null, "id": "ef42efec", "metadata": {}, "outputs": [], "source": ["ps = porter<PERSON><PERSON>mer()\n", "for w in words:\n", "    print(ps.stem(w))"]}], "metadata": {"kernelspec": {"display_name": "TLU", "language": "python", "name": "python3"}, "language_info": {"name": "python", "version": "3.10.18"}}, "nbformat": 4, "nbformat_minor": 5}