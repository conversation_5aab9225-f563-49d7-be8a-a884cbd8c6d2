import nltk
from nltk.stem import PorterStemmer
nltk.download('punkt')
nltk.download('punkt_tab')
nltk.download('stopwords')
nltk.download('wordnet')
nltk.download('averaged_perceptron_tagger')
nltk.download('maxent_ne_chunker')
nltk.download('words')

input ="Thuy Loi university is the best university in the world."

words = nltk.word_tokenize(input)
print(words)

ps = PorterStemmer()
for w in words:
    print(ps.stem(w))