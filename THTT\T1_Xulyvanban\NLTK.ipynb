import nltk
from nltk.stem import PorterStemmer
nltk.download('punkt')
nltk.download('punkt_tab')
nltk.download('stopwords')
nltk.download('wordnet')
nltk.download('averaged_perceptron_tagger')
nltk.download('maxent_ne_chunker')
nltk.download('words')

input ="Thuy Loi university is the best university in the world."

words = nltk.word_tokenize(input)
print(words)

ps = PorterStemmer()
for w in words:
    print(ps.stem(w))

from underthesea import word_tokenize

d = input("Nhập văn bản tiếng Việt: ")

t = input("Nhập từ cần kiểm tra: ")

words = word_tokenize(d)

if t in words:
    print(f"Từ '{t}' CÓ xuất hiện trong văn bản.")
else:
    print(f"Từ '{t}' KHÔNG xuất hiện trong văn bản.")

n = input(" Nhập một tài liệu: ")

words = word_tokenize(n)
 

freq = {}
for w in words:
    freq[w] = freq.get(w, 0) + 1

print("Tần số từ trong văn bản:")
for word, count in freq.items():
    print(f"{word}: {count}")

from underthesea import word_tokenize
import re

D = [
    "Students study natural language processing.",
    "Natural language processing is a part of artificial intelligence.",
    "The university has many hard working students.",
    "Artificial intelligence is developing rapidly."
]


def clean_word(w):
    w = w.lower()               # về chữ thường
    w = re.sub(r"[^\w\s]", "", w)  # bỏ dấu câu
    return w.strip()

doc_tokens = []
for doc in D:
    words = word_tokenize(doc)
    # tiền xử lý từng từ
    cleaned_words = {clean_word(w) for w in words if clean_word(w) != ""}
    doc_tokens.append(cleaned_words)

df = {}
for tokens in doc_tokens:
    for w in tokens:
        df[w] = df.get(w, 0) + 1

print("Từ vựng và số tài liệu chứa từ đó:")
for word in sorted(df.keys()):
    print(f"{word}: {df[word]}")

list1 = [1, 2, 4, 5, 7]
list2 = [2, 3, 5, 6]

intersection = sorted(list(set(list1) & set(list2)))
union = sorted(list(set(list1) | set(list2)))

print("Danh sách 1:", list1)
print("Danh sách 2:", list2)
print("Giao:", intersection)
print("Hợp:", union)

import os
folder = "documents"

documents = []
filenames = sorted(os.listdir(folder))
for fname in filenames:
    with open(os.path.join(folder, fname), "r", encoding="utf-8") as f:
        text = f.read()
        documents.append(text)

index = {}
for doc_id, text in enumerate(documents, start=1):  # doc_id = 1..N
    words = word_tokenize(text.lower())
    for w in set(words):  # set để tránh thêm trùng lặp trong cùng 1 tài liệu
        if w not in index:
            index[w] = []
        index[w].append(doc_id)

while True:
    query = input("Nhập từ cần tìm (hoặc 'exit' để thoát): ").strip().lower()
    if query == "exit":
        break
    if query in index:
        print(f"Từ '{query}' xuất hiện trong các tài liệu:", index[query])
    else:
        print(f"Từ '{query}' không có trong tập tài liệu.")

